# --- Standard Library Imports ---
from unittest import mock

# --- Third-Party Imports ---
from django.contrib.auth import authenticate
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import SimpleUploadedFile
from django.forms.models import model_to_dict
from model_bakery import baker
import pytest

# --- Local App Imports ---
from accounts_app.forms import (
    AccountDeactivationForm,
    CustomerLoginForm,
    CustomerProfileForm,
    CustomerSignupForm,
    ServiceProviderLoginForm,
    ServiceProviderProfileForm,
    ServiceProviderSignupForm,
    TeamMemberForm,
)
from accounts_app.models import (
    CustomUser,
    CustomerProfile,
    ServiceProviderProfile,
    TeamMember,
)
from admin_app.tests.test_utils import create_test_image


pytestmark = pytest.mark.django_db


# --- AccessibleFormMixin ---
def test_accessible_form_mixin_sets_labels():
    form = CustomerSignupForm()
    for field in form.fields.values():
        assert field.widget.attrs.get("aria-label") == field.label


# --- AccountDeactivationForm ---
def test_account_deactivation_form_valid_email():
    user = baker.make(CustomUser)
    form = AccountDeactivationForm(user, data={"confirm_email": user.email})
    assert form.is_valid()


def test_account_deactivation_form_email_mismatch():
    user = baker.make(CustomUser)
    form = AccountDeactivationForm(user, data={"confirm_email": "<EMAIL>"})
    assert not form.is_valid()
    assert "confirm_email" in form.errors


# --- CustomerSignupForm ---
def test_customer_signup_password_mismatch():
    data = {
        "email": "<EMAIL>",
        "password1": "pass12345",
        "password2": "wrong123",
        "agree_to_terms": True,
    }
    form = CustomerSignupForm(data=data)
    assert not form.is_valid()
    assert "password2" in form.errors


def test_customer_signup_duplicate_email():
    baker.make(CustomUser, email="<EMAIL>")
    data = {
        "email": "<EMAIL>",
        "password1": "StrongPass123",
        "password2": "StrongPass123",
        "agree_to_terms": True,
    }
    form = CustomerSignupForm(data=data)
    assert not form.is_valid()
    assert "email" in form.errors


def test_customer_signup_save_sets_role():
    data = {
        "email": "<EMAIL>",
        "password1": "StrongPass123",
        "password2": "StrongPass123",
        "agree_to_terms": True,
    }
    form = CustomerSignupForm(data=data)
    assert form.is_valid()
    user = form.save()
    assert user.role == CustomUser.CUSTOMER


def test_customer_signup_password_hashed():
    data = {
        "email": "<EMAIL>",
        "password1": "StrongPass123",
        "password2": "StrongPass123",
        "agree_to_terms": True,
    }
    form = CustomerSignupForm(data=data)
    assert form.is_valid()
    user = form.save()
    assert user.password != "StrongPass123"
    assert user.check_password("StrongPass123")


def test_customer_signup_terms_not_agreed():
    """Test that form is invalid when terms checkbox is not checked"""
    data = {
        "email": "<EMAIL>",
        "password1": "StrongPass123",
        "password2": "StrongPass123",
        "agree_to_terms": False,
    }
    form = CustomerSignupForm(data=data)
    assert not form.is_valid()
    assert "agree_to_terms" in form.errors
    assert "You must agree to the Terms of Service and Privacy Policy" in str(form.errors["agree_to_terms"])


def test_customer_signup_terms_missing():
    """Test that form is invalid when terms field is missing entirely"""
    data = {
        "email": "<EMAIL>",
        "password1": "StrongPass123",
        "password2": "StrongPass123",
    }
    form = CustomerSignupForm(data=data)
    assert not form.is_valid()
    assert "agree_to_terms" in form.errors


def test_customer_signup_terms_agreed():
    """Test that form is valid when terms checkbox is checked"""
    data = {
        "email": "<EMAIL>",
        "password1": "StrongPass123",
        "password2": "StrongPass123",
        "agree_to_terms": True,
    }
    form = CustomerSignupForm(data=data)
    assert form.is_valid()
    user = form.save()
    assert user.role == CustomUser.CUSTOMER


def test_customer_signup_weak_password_too_short():
    """Test that form rejects passwords that are too short"""
    data = {
        "email": "<EMAIL>",
        "password1": "123",  # Too short
        "password2": "123",
        "agree_to_terms": True,
    }
    form = CustomerSignupForm(data=data)
    assert not form.is_valid()
    assert "password1" in form.errors
    assert any("at least 8 characters" in str(error) for error in form.errors["password1"])


def test_customer_signup_weak_password_common():
    """Test that form rejects common passwords"""
    data = {
        "email": "<EMAIL>",
        "password1": "password",  # Common password
        "password2": "password",
        "agree_to_terms": True,
    }
    form = CustomerSignupForm(data=data)
    assert not form.is_valid()
    assert "password1" in form.errors
    assert any("too common" in str(error) for error in form.errors["password1"])


def test_customer_signup_weak_password_numeric():
    """Test that form rejects entirely numeric passwords"""
    data = {
        "email": "<EMAIL>",
        "password1": "12345678",  # All numeric
        "password2": "12345678",
        "agree_to_terms": True,
    }
    form = CustomerSignupForm(data=data)
    assert not form.is_valid()
    assert "password1" in form.errors
    assert any("entirely numeric" in str(error) for error in form.errors["password1"])


def test_customer_signup_weak_password_similar_to_email():
    """Test that form rejects passwords similar to user attributes"""
    data = {
        "email": "<EMAIL>",
        "password1": "testuser123",  # Similar to email
        "password2": "testuser123",
        "agree_to_terms": True,
    }
    form = CustomerSignupForm(data=data)
    assert not form.is_valid()
    assert "password1" in form.errors
    assert any("too similar" in str(error) for error in form.errors["password1"])


# --- CustomerLoginForm ---
def test_customer_login_success():
    user = CustomUser.objects.create_user("<EMAIL>", "pass123")
    form = CustomerLoginForm(data={"email": user.email, "password": "pass123"})
    assert form.is_valid()
    assert form.get_user() == user


def test_customer_login_inactive_account():
    user = CustomUser.objects.create_user("<EMAIL>", "pass123")
    user.is_active = False
    user.save()
    form = CustomerLoginForm(data={"email": user.email, "password": "pass123"})
    assert not form.is_valid()
    assert "__all__" in form.errors


def test_customer_login_wrong_password():
    user = CustomUser.objects.create_user("<EMAIL>", "pass123")
    form = CustomerLoginForm(data={"email": user.email, "password": "badpass"})
    assert not form.is_valid()
    assert "__all__" in form.errors


def test_customer_login_wrong_role():
    provider = CustomUser.objects.create_user(
        "<EMAIL>", "pass123", role=CustomUser.SERVICE_PROVIDER
    )
    form = CustomerLoginForm(data={"email": provider.email, "password": "pass123"})
    assert not form.is_valid()
    assert "__all__" in form.errors


# --- CustomerProfileForm ---
def test_customer_profile_birth_fields_to_none():
    user = baker.make(CustomUser)
    profile = baker.make(CustomerProfile, user=user)
    data = model_to_dict(profile)
    data["birth_month"] = ""
    data["birth_year"] = ""
    form = CustomerProfileForm(data=data, instance=profile)
    assert form.is_valid()
    assert form.cleaned_data["birth_month"] is None
    assert form.cleaned_data["birth_year"] is None


def test_customer_profile_phone_normalization():
    user = baker.make(CustomUser)
    profile = baker.make(CustomerProfile, user=user)
    data = model_to_dict(profile)
    data["phone_number"] = "************"
    form = CustomerProfileForm(data=data, instance=profile)
    assert form.is_valid()
    assert form.cleaned_data["phone_number"] == "+**********"


def test_customer_profile_invalid_phone_number():
    user = baker.make(CustomUser)
    profile = baker.make(CustomerProfile, user=user)
    data = model_to_dict(profile)
    data["phone_number"] = "123"
    form = CustomerProfileForm(data=data, instance=profile)
    assert not form.is_valid()
    assert "phone_number" in form.errors


def test_customer_profile_picture_invalid_format():
    user = baker.make(CustomUser)
    profile = baker.make(CustomerProfile, user=user)
    data = model_to_dict(profile)
    bad = SimpleUploadedFile("img.txt", b"bad", content_type="text/plain")
    form = CustomerProfileForm(data=data, files={"profile_picture": bad}, instance=profile)
    assert not form.is_valid()
    assert "profile_picture" in form.errors


# --- ServiceProviderSignupForm ---
def test_provider_signup_phone_normalization():
    data = {
        "email": "<EMAIL>",
        "password1": "StrongPass123",
        "password2": "StrongPass123",
        "business_name": "Test Spa",
        "business_phone_number": "(*************",
        "contact_person_name": "Owner",
        "business_address": "1 Way",
        "city": "Town",
        "state": ServiceProviderProfile.CALIFORNIA,
        "zip_code": "12345",
    }
    form = ServiceProviderSignupForm(data=data)
    assert form.is_valid()
    assert form.cleaned_data["business_phone_number"] == "+**********"


def test_provider_signup_invalid_phone_number():
    data = {
        "email": "<EMAIL>",
        "password1": "StrongPass123",
        "password2": "StrongPass123",
        "business_name": "Test Spa",
        "business_phone_number": "123",
        "contact_person_name": "Owner",
        "business_address": "1 Way",
        "city": "Town",
        "state": ServiceProviderProfile.CALIFORNIA,
        "zip_code": "12345",
    }
    form = ServiceProviderSignupForm(data=data)
    assert not form.is_valid()
    assert "business_phone_number" in form.errors


def test_provider_signup_duplicate_email():
    baker.make(CustomUser, email="<EMAIL>")
    data = {
        "email": "<EMAIL>",
        "password1": "StrongPass123",
        "password2": "StrongPass123",
        "business_name": "Dup", 
        "business_phone_number": "**********",
        "contact_person_name": "D", 
        "business_address": "1 St", 
        "city": "Town",
        "state": ServiceProviderProfile.CALIFORNIA,
        "zip_code": "12345",
    }
    form = ServiceProviderSignupForm(data=data)
    assert not form.is_valid()
    assert "email" in form.errors


def test_provider_signup_password_mismatch():
    data = {
        "email": "<EMAIL>",
        "password1": "StrongPass123",
        "password2": "WrongPass123",
        "business_name": "Spa",
        "business_phone_number": "**********",
        "contact_person_name": "Owner",
        "business_address": "1 St",
        "city": "Town",
        "state": ServiceProviderProfile.CALIFORNIA,
        "zip_code": "12345",
    }
    form = ServiceProviderSignupForm(data=data)
    assert not form.is_valid()
    assert "password2" in form.errors


def test_provider_signup_password_hashed():
    data = {
        "email": "<EMAIL>",
        "password1": "StrongPass123",
        "password2": "StrongPass123",
        "business_name": "Spa",
        "business_phone_number": "**********",
        "contact_person_name": "Owner",
        "business_address": "1 St",
        "city": "Town",
        "state": ServiceProviderProfile.CALIFORNIA,
        "zip_code": "12345",
    }
    form = ServiceProviderSignupForm(data=data)
    assert form.is_valid()
    user = form.save()
    assert user.password != "StrongPass123"
    assert user.check_password("StrongPass123")


# --- ServiceProviderLoginForm ---
def test_provider_login_inactive():
    user = CustomUser.objects.create_user(
        "<EMAIL>",
        "pass123",
        role=CustomUser.SERVICE_PROVIDER,
    )
    user.is_active = False
    user.save()
    form = ServiceProviderLoginForm(data={"email": user.email, "password": "pass123"})
    assert not form.is_valid()
    assert "__all__" in form.errors


def test_provider_login_success():
    user = CustomUser.objects.create_user(
        "<EMAIL>", "pass123", role=CustomUser.SERVICE_PROVIDER
    )
    form = ServiceProviderLoginForm(data={"email": user.email, "password": "pass123"})
    assert form.is_valid()
    assert form.get_user() == user


def test_provider_login_wrong_password():
    user = CustomUser.objects.create_user(
        "<EMAIL>", "pass123", role=CustomUser.SERVICE_PROVIDER
    )
    form = ServiceProviderLoginForm(data={"email": user.email, "password": "bad"})
    assert not form.is_valid()
    assert "__all__" in form.errors


def test_provider_login_wrong_role():
    customer = CustomUser.objects.create_user("<EMAIL>", "pass123")
    form = ServiceProviderLoginForm(data={"email": customer.email, "password": "pass123"})
    assert not form.is_valid()
    assert "__all__" in form.errors


# --- ServiceProviderProfileForm ---
def test_provider_profile_logo_invalid_format():
    profile = baker.make(ServiceProviderProfile)
    data = model_to_dict(profile)
    invalid = SimpleUploadedFile("logo.txt", b"bad", content_type="text/plain")
    form = ServiceProviderProfileForm(
        data=data,
        files={"logo": invalid},
        instance=profile,
    )
    assert not form.is_valid()
    assert "logo" in form.errors


def test_provider_profile_logo_too_large():
    profile = baker.make(ServiceProviderProfile)
    data = model_to_dict(profile)
    big = SimpleUploadedFile(
        "logo.jpg",
        b"a" * (2 * 1024 * 1024 + 1),
        content_type="image/jpeg",
    )
    form = ServiceProviderProfileForm(data=data, files={"logo": big}, instance=profile)
    assert not form.is_valid()
    assert "logo" in form.errors


def test_provider_profile_logo_valid_format_and_size():
    profile = baker.make(ServiceProviderProfile)
    data = model_to_dict(profile)
    data["phone"] = "+**********"
    data["zip_code"] = "12345"
    form = ServiceProviderProfileForm(data=data, instance=profile)
    assert form.is_valid()


def test_provider_profile_logo_processing_error():
    profile = baker.make(ServiceProviderProfile)
    data = model_to_dict(profile)
    data["phone"] = "+**********"
    data["zip_code"] = "12345"
    good = create_test_image("logo.jpg")
    class FailingUploadForm:
        def __init__(self, *args, **kwargs):
            raise Exception("boom")

    with mock.patch("accounts_app.forms.provider.ImageUploadForm", FailingUploadForm):
        form = ServiceProviderProfileForm(data=data, files={"logo": good}, instance=profile)
        assert form.is_valid(), f"Form should remain valid despite processing error: {form.errors}"


# --- TeamMemberForm ---
def test_team_member_picture_invalid_format():
    provider = baker.make(ServiceProviderProfile)
    member = baker.make(TeamMember, service_provider=provider)
    data = {
        "name": "Alice",
        "position": "Therapist",
        "is_active": True,
    }
    bad_image = SimpleUploadedFile("photo.txt", b"bad", content_type="text/plain")
    form = TeamMemberForm(data=data, files={"photo": bad_image}, instance=member)
    assert not form.is_valid()
    assert "photo" in form.errors


def test_team_member_picture_valid_and_saved():
    provider = baker.make(ServiceProviderProfile)
    member = baker.make(TeamMember, service_provider=provider)
    data = {
        "name": "Bob",
        "position": "Manager",
        "is_active": True,
    }
    good = create_test_image("staff.jpg")
    with mock.patch("accounts_app.forms.team.ImageUploadForm", None), \
            mock.patch("accounts_app.forms.team.ImageService", None):
        form = TeamMemberForm(data=data, files={"photo": good}, instance=member)
        assert form.is_valid()


def test_team_member_picture_processing_error():
    """Test that team member form handles image processing gracefully."""
    provider = baker.make(ServiceProviderProfile)
    member = baker.make(TeamMember, service_provider=provider)
    data = {
        "name": "Carl",
        "position": "Therapist",
        "is_active": True,
    }
    good = create_test_image("staff.jpg")

    # With simplified validation, valid images should always pass
    form = TeamMemberForm(data=data, files={"photo": good}, instance=member)
    assert form.is_valid(), f"Form should be valid with good image: {form.errors}"


def test_team_member_edit_form_prefills_data():
    """Test that team member edit form pre-fills with existing data."""
    provider = baker.make(ServiceProviderProfile)
    member = baker.make(
        TeamMember,
        service_provider=provider,
        name="John Doe",
        position="Massage Therapist",
        is_active=True
    )

    # Create form with instance (edit mode)
    form = TeamMemberForm(instance=member)

    # Check that fields are pre-filled using the actual field names
    assert form.initial['name'] == "John Doe"
    assert form.initial['position'] == "Massage Therapist"
    assert form.initial['is_active'] is True


def test_team_member_edit_form_saves_correctly():
    """Test that team member edit form saves changes correctly."""
    provider = baker.make(ServiceProviderProfile)
    member = baker.make(
        TeamMember,
        service_provider=provider,
        name="John Doe",
        position="Massage Therapist",
        is_active=True
    )

    # Update data
    data = {
        "name": "Jane Smith",
        "position": "Esthetician",
        "is_active": False,
    }

    form = TeamMemberForm(data=data, instance=member)
    assert form.is_valid(), f"Form should be valid: {form.errors}"

    updated_member = form.save()
    assert updated_member.name == "Jane Smith"
    assert updated_member.position == "Esthetician"
    assert updated_member.is_active is False


def test_team_member_image_upload_valid_formats():
    """Test that team member form accepts valid image formats."""
    provider = baker.make(ServiceProviderProfile)
    member = baker.make(TeamMember, service_provider=provider)

    # Test JPG
    jpg_image = create_test_image("test.jpg")
    data = {
        "name": "Test User",
        "position": "Therapist",
        "is_active": True,
    }
    form = TeamMemberForm(data=data, files={"photo": jpg_image}, instance=member)
    assert form.is_valid(), f"JPG should be valid: {form.errors}"

    # Test PNG
    png_image = create_test_image("test.png")
    form = TeamMemberForm(data=data, files={"photo": png_image}, instance=member)
    assert form.is_valid(), f"PNG should be valid: {form.errors}"


def test_team_member_image_upload_invalid_format():
    """Test that team member form rejects invalid image formats."""
    provider = baker.make(ServiceProviderProfile)
    member = baker.make(TeamMember, service_provider=provider)

    # Create a fake GIF file (invalid format)
    gif_content = b'GIF89a\x01\x00\x01\x00\x00\x00\x00!'
    gif_file = SimpleUploadedFile("test.gif", gif_content, content_type="image/gif")

    data = {
        "name": "Test User",
        "position": "Therapist",
        "is_active": True,
    }
    form = TeamMemberForm(data=data, files={"photo": gif_file}, instance=member)
    assert not form.is_valid()
    assert "photo" in form.errors


def test_team_member_image_upload_too_large():
    """Test that team member form rejects oversized images."""
    provider = baker.make(ServiceProviderProfile)
    member = baker.make(TeamMember, service_provider=provider)

    # Create a large fake image file (over 5MB)
    large_content = b'fake_image_data' * (6 * 1024 * 1024 // 15)  # ~6MB
    large_file = SimpleUploadedFile("large.jpg", large_content, content_type="image/jpeg")

    data = {
        "name": "Test User",
        "position": "Therapist",
        "is_active": True,
    }
    form = TeamMemberForm(data=data, files={"photo": large_file}, instance=member)
    assert not form.is_valid()
    assert "photo" in form.errors


def test_customer_profile_edit_form_prefills_data():
    """Test that customer profile edit form pre-fills with existing data."""
    user = baker.make(CustomUser, role='customer')
    profile = baker.make(
        CustomerProfile,
        user=user,
        first_name="John",
        last_name="Doe",
        phone_number="+**********",
        gender="M"
    )

    form = CustomerProfileForm(instance=profile)

    # Check that fields are pre-filled
    assert form.initial['first_name'] == "John"
    assert form.initial['last_name'] == "Doe"
    assert form.initial['phone_number'] == "+**********"
    assert form.initial['gender'] == "M"


def test_customer_profile_image_upload_valid_formats():
    """Test that customer profile form accepts valid image formats."""
    user = baker.make(CustomUser, role='customer')
    profile = baker.make(CustomerProfile, user=user)

    # Test JPG
    jpg_image = create_test_image("profile.jpg")
    data = {
        "first_name": "Test",
        "last_name": "User",
    }
    form = CustomerProfileForm(data=data, files={"profile_picture": jpg_image}, instance=profile)
    assert form.is_valid(), f"JPG should be valid: {form.errors}"

    # Test PNG
    png_image = create_test_image("profile.png")
    form = CustomerProfileForm(data=data, files={"profile_picture": png_image}, instance=profile)
    assert form.is_valid(), f"PNG should be valid: {form.errors}"


def test_service_provider_profile_edit_form_prefills_data():
    """Test that service provider profile edit form pre-fills with existing data."""
    user = baker.make(CustomUser, role='service_provider')
    profile = baker.make(
        ServiceProviderProfile,
        user=user,
        legal_name="Test Business LLC",
        display_name="Test Business",
        phone="+**********",
        is_public=True
    )

    form = ServiceProviderProfileForm(instance=profile)

    # Check that fields are pre-filled
    assert form.initial['legal_name'] == "Test Business LLC"
    assert form.initial['display_name'] == "Test Business"
    assert form.initial['phone'] == "+**********"
    assert form.initial['is_public'] is True


def test_service_provider_logo_upload_valid_formats():
    """Test that service provider profile form accepts valid logo formats."""
    user = baker.make(CustomUser, role='service_provider')
    profile = baker.make(ServiceProviderProfile, user=user)

    # Test JPG
    jpg_image = create_test_image("logo.jpg")
    data = {
        "legal_name": "Test Business",
        "phone": "+**********",
        "contact_name": "John Doe",
        "address": "123 Test St",
        "city": "Test City",
        "state": "CA",
        "zip_code": "12345",
    }
    form = ServiceProviderProfileForm(data=data, files={"logo": jpg_image}, instance=profile)
    assert form.is_valid(), f"JPG should be valid: {form.errors}"

    # Test PNG
    png_image = create_test_image("logo.png")
    form = ServiceProviderProfileForm(data=data, files={"logo": png_image}, instance=profile)
    assert form.is_valid(), f"PNG should be valid: {form.errors}"
